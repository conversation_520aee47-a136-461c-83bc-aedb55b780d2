/**
 * Utility functions for date formatting and validation
 */

/**
 * Safely formats a date value to a localized date string
 * @param dateValue - The date value to format (can be string, Date, array, or null/undefined)
 * @param options - Intl.DateTimeFormatOptions for formatting
 * @returns Formatted date string or 'Not set' if invalid
 */
export const formatDate = (
  dateValue: string | Date | any[] | null | undefined,
  options: Intl.DateTimeFormatOptions = {}
): string => {
  if (!dateValue) {
    return 'Not set';
  }

  try {
    let date: Date;

    // Handle array format from backend [year, month, day, hour, minute, second, nanosecond]
    if (Array.isArray(dateValue)) {
      if (dateValue.length >= 3) {
        // Month is 0-indexed in JavaScript Date constructor
        const year = dateValue[0];
        const month = dateValue[1] - 1; // Convert to 0-indexed
        const day = dateValue[2];
        const hour = dateValue[3] || 0;
        const minute = dateValue[4] || 0;
        const second = dateValue[5] || 0;

        date = new Date(year, month, day, hour, minute, second);
      } else {
        return 'Invalid date';
      }
    } else {
      date = new Date(dateValue);
    }

    // Check if the date is valid
    if (isNaN(date.getTime())) {
      return 'Invalid date';
    }

    return date.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      ...options
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid date';
  }
};

/**
 * Safely formats a date value to a localized date and time string
 * @param dateValue - The date value to format (can be string, Date, array, or null/undefined)
 * @param options - Intl.DateTimeFormatOptions for formatting
 * @returns Formatted date and time string or 'Not set' if invalid
 */
export const formatDateTime = (
  dateValue: string | Date | any[] | null | undefined,
  options: Intl.DateTimeFormatOptions = {}
): string => {
  if (!dateValue) {
    return 'Not set';
  }

  try {
    let date: Date;

    // Handle array format from backend [year, month, day, hour, minute, second, nanosecond]
    if (Array.isArray(dateValue)) {
      if (dateValue.length >= 6) {
        // Month is 0-indexed in JavaScript Date constructor
        const year = dateValue[0];
        const month = dateValue[1] - 1; // Convert to 0-indexed
        const day = dateValue[2];
        const hour = dateValue[3];
        const minute = dateValue[4];
        const second = dateValue[5];

        date = new Date(year, month, day, hour, minute, second);
      } else if (dateValue.length >= 3) {
        const year = dateValue[0];
        const month = dateValue[1] - 1; // Convert to 0-indexed
        const day = dateValue[2];

        date = new Date(year, month, day);
      } else {
        return 'Invalid date';
      }
    } else {
      date = new Date(dateValue);
    }

    // Check if the date is valid
    if (isNaN(date.getTime())) {
      return 'Invalid date';
    }

    return date.toLocaleString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      ...options
    });
  } catch (error) {
    console.error('Error formatting date time:', error);
    return 'Invalid date';
  }
};

/**
 * Checks if a date value is valid
 * @param dateValue - The date value to check
 * @returns true if the date is valid, false otherwise
 */
export const isValidDate = (dateValue: string | Date | null | undefined): boolean => {
  if (!dateValue) {
    return false;
  }

  try {
    const date = new Date(dateValue);
    return !isNaN(date.getTime());
  } catch (error) {
    return false;
  }
};

/**
 * Formats a date for input fields (YYYY-MM-DD format)
 * @param dateValue - The date value to format
 * @returns Formatted date string for input fields or empty string if invalid
 */
export const formatDateForInput = (dateValue: string | Date | null | undefined): string => {
  if (!dateValue) {
    return '';
  }

  try {
    const date = new Date(dateValue);
    
    if (isNaN(date.getTime())) {
      return '';
    }

    return date.toISOString().split('T')[0];
  } catch (error) {
    console.error('Error formatting date for input:', error);
    return '';
  }
};

/**
 * Gets relative time string (e.g., "2 days ago", "in 3 hours")
 * @param dateValue - The date value to compare
 * @returns Relative time string or 'Invalid date' if invalid
 */
export const getRelativeTime = (dateValue: string | Date | null | undefined): string => {
  if (!dateValue) {
    return 'Not set';
  }

  try {
    const date = new Date(dateValue);
    
    if (isNaN(date.getTime())) {
      return 'Invalid date';
    }

    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMinutes / 60);
    const diffInDays = Math.floor(diffInHours / 24);

    if (Math.abs(diffInMinutes) < 1) {
      return 'Just now';
    } else if (Math.abs(diffInMinutes) < 60) {
      return diffInMinutes > 0 ? `${diffInMinutes} minutes ago` : `in ${Math.abs(diffInMinutes)} minutes`;
    } else if (Math.abs(diffInHours) < 24) {
      return diffInHours > 0 ? `${diffInHours} hours ago` : `in ${Math.abs(diffInHours)} hours`;
    } else if (Math.abs(diffInDays) < 30) {
      return diffInDays > 0 ? `${diffInDays} days ago` : `in ${Math.abs(diffInDays)} days`;
    } else {
      return formatDate(dateValue);
    }
  } catch (error) {
    console.error('Error getting relative time:', error);
    return 'Invalid date';
  }
};
