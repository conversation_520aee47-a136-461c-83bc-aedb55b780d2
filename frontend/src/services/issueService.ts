import api from './apiService';
import { API_ENDPOINTS } from '../config/apiConfig';

// Real API implementations
const getIssues = async (params: any) => {
  console.log('Fetching issues with params:', params);

  // Format date parameters if they exist
  const formattedParams = { ...params };
  if (formattedParams.startDate) {
    formattedParams.startDate = formattedParams.startDate instanceof Date
      ? formattedParams.startDate.toISOString().split('T')[0]
      : formattedParams.startDate;
  }
  if (formattedParams.endDate) {
    formattedParams.endDate = formattedParams.endDate instanceof Date
      ? formattedParams.endDate.toISOString().split('T')[0]
      : formattedParams.endDate;
  }

  const response = await api.get(API_ENDPOINTS.ISSUES.BASE, { params: formattedParams });
  console.log('Issues response:', response.data);

  // Ensure we always return an array
  if (response.data && !Array.isArray(response.data)) {
    console.warn('API returned non-array data for issues. Converting to array.');
    // If response.data.content exists and is an array (Spring Data pagination), use it
    if (response.data.content && Array.isArray(response.data.content)) {
      return response.data.content;
    }
    // Otherwise return an empty array
    return [];
  }

  return response.data;
};

const getIssueById = async (id: string | number) => {
  console.log(`Fetching issue with ID: ${id}`);
  const response = await api.get(API_ENDPOINTS.ISSUES.BY_ID(id));
  console.log(`Issue ${id} response:`, response.data);
  return response.data;
};

const createIssue = async (issueData: any) => {
  console.log('Creating issue with data:', issueData);
  console.log('API endpoint:', API_ENDPOINTS.ISSUES.BASE);
  try {
    const response = await api.post(API_ENDPOINTS.ISSUES.BASE, issueData);
    console.log('Create issue response:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('Error in createIssue API call:', error);
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }
    throw error;
  }
};

const updateIssue = async (id: string | number, issueData: any) => {
  console.log(`Updating issue ${id} with data:`, issueData);
  const response = await api.put(API_ENDPOINTS.ISSUES.BY_ID(id), issueData);
  console.log(`Update issue ${id} response:`, response.data);
  return response.data;
};

const updateIssueStatus = async (id: string | number, status: string) => {
  console.log(`Updating issue ${id} status to: ${status}`);
  const response = await api.patch(API_ENDPOINTS.ISSUES.STATUS(id), { status });
  console.log(`Update issue ${id} status response:`, response.data);
  return response.data;
};

const deleteIssue = async (id: string | number) => {
  console.log(`Deleting issue ${id}`);
  await api.delete(API_ENDPOINTS.ISSUES.BY_ID(id));
  console.log(`Issue ${id} deleted successfully`);
  return { success: true };
};

// Comment operations
const addComment = async (issueId: string | number, content: string) => {
  console.log(`Adding comment to issue ${issueId} with content:`, content);
  const commentData = { content };
  const response = await api.post(API_ENDPOINTS.COMMENTS.BY_ISSUE(issueId), commentData);
  console.log(`Add comment response:`, response.data);
  return response.data;
};

const deleteComment = async (commentId: string | number) => {
  console.log(`Deleting comment ${commentId}`);
  await api.delete(API_ENDPOINTS.COMMENTS.BY_ID(commentId));
  console.log(`Comment ${commentId} deleted successfully`);
  return { success: true };
};

// Attachment operations
const addAttachment = async (issueId: string | number, formData: FormData) => {
  console.log(`Adding attachment to issue ${issueId}`);
  const response = await api.post(API_ENDPOINTS.ATTACHMENTS.BY_ISSUE(issueId), formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  console.log(`Add attachment response:`, response.data);
  return response.data;
};

const deleteAttachment = async (attachmentId: string | number) => {
  console.log(`Deleting attachment ${attachmentId}`);
  await api.delete(API_ENDPOINTS.ATTACHMENTS.BY_ID(attachmentId));
  console.log(`Attachment ${attachmentId} deleted successfully`);
  return { success: true };
};

// Watcher operations
const addWatcher = async (issueId: string | number, userId: string | number) => {
  console.log(`Adding watcher ${userId} to issue ${issueId}`);
  const response = await api.post(API_ENDPOINTS.ISSUES.WATCHERS.ADD(issueId, userId));
  console.log(`Add watcher response:`, response.data);
  return response.data;
};

const removeWatcher = async (issueId: string | number, userId: string | number) => {
  console.log(`Removing watcher ${userId} from issue ${issueId}`);
  await api.delete(API_ENDPOINTS.ISSUES.WATCHERS.REMOVE(issueId, userId));
  console.log(`Watcher ${userId} removed from issue ${issueId} successfully`);
  return { success: true };
};

// Dashboard and reporting
const getDashboardStats = async (params: { period?: string; environment?: string } = {}) => {
  const { period = 'current_week', environment } = params;
  console.log(`Fetching dashboard stats for period: ${period}, environment: ${environment}`);
  const queryParams: any = { period };
  if (environment) {
    queryParams.environment = environment;
  }
  const response = await api.get(API_ENDPOINTS.ISSUES.STATS, { params: queryParams });
  console.log('Dashboard stats response:', response.data);
  return response.data;
};

const getRecentIssues = async (limit: number = 5) => {
  console.log(`Fetching recent issues with limit: ${limit}`);
  const response = await api.get(API_ENDPOINTS.ISSUES.RECENT, { params: { limit } });
  console.log('Recent issues response:', response.data);
  return Array.isArray(response.data) ? response.data : [];
};

const getOverdueIssues = async () => {
  console.log('Fetching overdue issues');
  const response = await api.get(API_ENDPOINTS.ISSUES.OVERDUE);
  console.log('Overdue issues response:', response.data);
  return Array.isArray(response.data) ? response.data : [];
};

const getCustomReport = async (params: any) => {
  console.log('Fetching custom report with params:', params);
  const response = await api.get(API_ENDPOINTS.ISSUES.REPORT, { params });
  console.log('Custom report response:', response.data);
  return response.data;
};

const bulkCreateIssues = async (issues: any[]) => {
  console.log('Bulk creating issues:', issues.length);
  const response = await api.post(API_ENDPOINTS.ISSUES.BULK, { issues });
  console.log('Bulk create issues response:', response.data);
  return response.data;
};

// Get lookup values for dropdowns
const getLookupValues = async () => {
  console.log('Fetching lookup values');
  console.log('API base URL:', api.defaults.baseURL);
  console.log('API headers:', api.defaults.headers);

  try {
    const response = await api.get(API_ENDPOINTS.LOOKUPS.BASE);
    console.log('Lookup values response:', response.data);
    console.log('Response status:', response.status);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching lookup values:', error);
    console.error('Error response:', error.response?.data);
    console.error('Error status:', error.response?.status);
    throw error;
  }
};

// AI Assistance
const getAIAssistance = async (requestData: {
  briefDescription: string;
  context?: string;
  environment?: string;
  module?: string;
  useAdvancedAI?: boolean;
}) => {
  console.log('Requesting AI assistance with data:', requestData);
  try {
    const response = await api.post(API_ENDPOINTS.ISSUES.AI_ASSIST, requestData);
    console.log('AI assistance response:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('Error getting AI assistance:', error);
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }
    throw error;
  }
};

const issueService = {
  getIssues,
  getIssueById,
  createIssue,
  updateIssue,
  updateIssueStatus,
  deleteIssue,
  addComment,
  deleteComment,
  addAttachment,
  deleteAttachment,
  addWatcher,
  removeWatcher,
  getDashboardStats,
  getRecentIssues,
  getOverdueIssues,
  getCustomReport,
  bulkCreateIssues,
  getLookupValues,
  getAIAssistance
};

export default issueService;
