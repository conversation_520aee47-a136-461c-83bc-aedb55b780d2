import { useEffect, useState, useMemo } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Box, Button, FormControl, FormHelperText, Grid, InputLabel,
  MenuItem, Paper, Select, TextField, Typography, CircularProgress,
  Switch, FormControlLabel, Chip, Divider, Alert
} from '@mui/material';
import { SmartToy as AIIcon, Mic as MicIcon, RecordVoiceOver as VoiceIcon } from '@mui/icons-material';
import { createIssue, updateIssue } from '../store/slices/issueSlice';
import { fetchUsers } from '../store/slices/userSlice';
import { fetchLookupValues } from '../store/slices/lookupSlice';
import {
  fetchModulesWithSubmodulesAsync,
  fetchSubmodulesByModuleIdAsync,
  selectModuleOptions,
  selectSubmoduleOptionsForModule,
  selectModulesLoading
} from '../store/slices/moduleSlice';
import { RootState, AppDispatch } from '../store';
import AIAssistDialog from './AIAssistDialog';
import issueTypeFieldConfigService, { FieldDisplayInfo } from '../services/issueTypeFieldConfigService';

interface IssueFormProps {
  issueId?: number;
  initialData?: any;
  onSubmitSuccess: (isCancel?: boolean) => void;
  onCancel?: () => void;
  isEditMode?: boolean;
}

const IssueForm: React.FC<IssueFormProps> = ({ issueId, initialData, onSubmitSuccess, onCancel, isEditMode = false }) => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const location = useLocation();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [aiAssistEnabled, setAiAssistEnabled] = useState(false);
  const [aiDialogOpen, setAiDialogOpen] = useState(false);
  const [aiGenerated, setAiGenerated] = useState(false);
  const [voiceGenerated, setVoiceGenerated] = useState(false);

  // Conditional fields state
  const [conditionalFields, setConditionalFields] = useState<{
    required: string[];
    optional: string[];
    displayInfo: Record<string, FieldDisplayInfo>;
  }>({
    required: [],
    optional: [],
    displayInfo: {}
  });

  const { users } = useSelector((state: RootState) => state.users);
  const {
    issueType,
    issueStatus,
    issueEnvironment,
    issuePriority,
    issueSeverity,
    loading: lookupLoading
  } = useSelector((state: RootState) => state.lookup);

  // Module and submodule state
  const moduleOptions = useSelector(selectModuleOptions);
  const modulesLoading = useSelector(selectModulesLoading);
  const [selectedModuleId, setSelectedModuleId] = useState<number | null>(
    initialData?.module?.id || null
  );
  const submoduleOptions = useSelector((state: RootState) =>
    selectedModuleId ? selectSubmoduleOptionsForModule(state, selectedModuleId) : []
  );

  // Define common styles for form fields
  const commonFieldSx = {
    '& .MuiOutlinedInput-root': {
      borderRadius: 8,
      // Make input fields more compact
      '& input': {
        padding: '10px 14px',
        fontWeight: 'normal',
        color: 'text.primary',
        '&::placeholder': {
          color: 'text.disabled',
          opacity: 0.6,
          fontWeight: 'normal'
        }
      },
      '&:hover .MuiOutlinedInput-notchedOutline': {
        borderColor: 'primary.main',
        borderWidth: 1
      },
      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
        borderColor: 'primary.main',
        borderWidth: 2
      }
    },
    '& .MuiInputBase-input::placeholder': {
      color: 'text.disabled',
      opacity: 0.6,
      fontWeight: 'normal'
    },
    '& .MuiInputLabel-root': {
      color: 'text.secondary',
      '&.Mui-focused': {
        color: 'primary.main'
      }
    }
  };

  // Define styles for text areas
  const textAreaSx = {
    ...commonFieldSx,
    '& .MuiOutlinedInput-root': {
      ...commonFieldSx['& .MuiOutlinedInput-root'],
      padding: 1,
      '& textarea': {
        fontWeight: 'normal',
        color: 'text.primary',
        minHeight: '80px', // Same as description field
        '&::placeholder': {
          color: 'text.disabled',
          opacity: 0.6,
          fontWeight: 'normal'
        }
      }
    },
    '& .MuiInputBase-input::placeholder': {
      color: 'text.disabled',
      opacity: 0.6,
      fontWeight: 'normal'
    }
  };

  // Format the date from the backend for form input (YYYY-MM-DD)
  const formatDateFromBackend = (dateValue: number[] | string | null | undefined) => {
    // Handle array format [year, month, day]
    if (dateValue && Array.isArray(dateValue) && dateValue.length >= 3) {
      const [year, month, day] = dateValue;
      // Create a date string in YYYY-MM-DD format
      return `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
    }

    // Handle string format (ISO date string)
    if (dateValue && typeof dateValue === 'string') {
      try {
        const date = new Date(dateValue);
        if (!isNaN(date.getTime())) {
          // Format as YYYY-MM-DD
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          return `${year}-${month}-${day}`;
        }
      } catch (error) {
        console.error('Error parsing date string:', error);
      }
    }

    // Return empty string if date is invalid or not provided
    return '';
  };

  // Format date for display (MM/DD/YYYY)
  const formatDateDisplay = (dateValue: number[] | string | null | undefined) => {
    try {
      // Handle array format [year, month, day]
      if (dateValue && Array.isArray(dateValue) && dateValue.length >= 3) {
        const [year, month, day] = dateValue;
        return `${month}/${day}/${year}`;
      }

      // Handle string format (ISO date string or YYYY-MM-DD)
      if (dateValue && typeof dateValue === 'string') {
        // If it's already in YYYY-MM-DD format
        if (dateValue.match(/^\d{4}-\d{2}-\d{2}$/)) {
          const [year, month, day] = dateValue.split('-').map(Number);
          return `${month}/${day}/${year}`;
        }

        // Otherwise try to parse as date
        const date = new Date(dateValue);
        if (!isNaN(date.getTime())) {
          return `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear()}`;
        }
      }

      return 'Invalid Date';
    } catch (error) {
      console.error('Error formatting date for display:', error);
      return 'Invalid Date';
    }
  };

  // Fetch lookup values, users, and modules when component mounts
  useEffect(() => {
    console.log('Fetching lookup values, users, and modules...');
    dispatch(fetchLookupValues());
    dispatch(fetchUsers());
    dispatch(fetchModulesWithSubmodulesAsync());
  }, [dispatch]);

  // Fetch submodules when module changes
  useEffect(() => {
    if (selectedModuleId) {
      console.log('Fetching submodules for module:', selectedModuleId);
      dispatch(fetchSubmodulesByModuleIdAsync(selectedModuleId));
    }
  }, [dispatch, selectedModuleId]);

  // Create a separate state for the current issue type to avoid dependency issues
  const [currentIssueType, setCurrentIssueType] = useState<string>('');

  // Form validation schema with dynamic conditional field validation
  const validationSchema = useMemo(() => Yup.object().shape({
    title: Yup.string().required('Title is required'),
    description: Yup.string().required('Description is required'),
    type: Yup.string().required('Type is required'),
    severity: Yup.string().required('Severity is required'),
    priority: Yup.string().required('Priority is required'),
    status: Yup.string().required('Status is required'),
    environment: Yup.string().required('Environment is required'),
    rootCause: Yup.string().when('status', {
      is: 'DEV_COMPLETED',
      then: (schema) => schema.required('Root Cause is required when status is Dev Completed'),
      otherwise: (schema) => schema
    }),
    qcCompletionDate: Yup.string().when('devCompletionDate', {
      is: (devCompletionDate: string) => devCompletionDate && devCompletionDate.length > 0,
      then: (schema) => schema.test(
        'qc-after-dev',
        'QC Completion Date cannot be earlier than Dev Completion Date',
        function(value) {
          if (!value) return true; // Allow empty QC date
          const devDate = this.parent.devCompletionDate;
          if (!devDate) return true; // Allow if no dev date
          return new Date(value) >= new Date(devDate);
        }
      ),
      otherwise: (schema) => schema
    }),
    // Dynamic conditional field validation - Bug fields are now optional
    stepsToReproduce: Yup.string(),
    expectedResult: Yup.string(),
    actualResult: Yup.string(),
    businessJustification: Yup.string().when('type', {
      is: (type: string) => ['FEATURE', 'EPIC'].includes(type),
      then: (schema) => schema.required('Business Justification is required for this issue type'),
      otherwise: (schema) => schema
    }),
    acceptanceCriteria: Yup.string().when('type', {
      is: (type: string) => ['FEATURE', 'STORY'].includes(type),
      then: (schema) => schema.required('Acceptance Criteria is required for this issue type'),
      otherwise: (schema) => schema
    }),
    userStory: Yup.string().when('type', {
      is: 'STORY',
      then: (schema) => schema.required('User Story is required for Story type issues'),
      otherwise: (schema) => schema
    }),
    definitionOfDone: Yup.string().when('type', {
      is: 'EPIC',
      then: (schema) => schema.required('Definition of Done is required for Epic type issues'),
      otherwise: (schema) => schema
    }),
    researchNotes: Yup.string().when('type', {
      is: 'SPIKE',
      then: (schema) => schema.required('Research Notes is required for Spike type issues'),
      otherwise: (schema) => schema
    }),
    technicalApproach: Yup.string().when('type', {
      is: (type: string) => ['ENHANCEMENT', 'TASK'].includes(type),
      then: (schema) => schema.required('Technical Approach is required for this issue type'),
      otherwise: (schema) => schema
    })
  }), []);

  // Initialize form with default values or data from props
  const formik = useFormik({
    initialValues: initialData ? {
      title: initialData.title || '',
      description: initialData.description || '',
      type: initialData.type || '',
      severity: initialData.severity || '',
      priority: initialData.priority || '',
      status: initialData.status || '',
      assigneeId: initialData.assignee?.id?.toString() || '',
      devCompletionDate: formatDateFromBackend(initialData.devCompletionDate || initialData.targetDate) || '',
      qcCompletionDate: formatDateFromBackend(initialData.qcCompletionDate) || '',
      rootCause: initialData.rootCause || '',
      targetDateChangeReason: '',
      versionRelease: initialData.versionRelease || '',
      parentId: initialData.parent?.id?.toString() || '',
      environment: initialData.environment || '',
      moduleId: initialData.module?.id?.toString() || '',
      submoduleId: initialData.submodule?.id?.toString() || '',
      reopenComment: '',
      // Conditional fields
      stepsToReproduce: initialData.stepsToReproduce || '',
      expectedResult: initialData.expectedResult || '',
      actualResult: initialData.actualResult || '',
      businessJustification: initialData.businessJustification || '',
      acceptanceCriteria: initialData.acceptanceCriteria || '',
      userStory: initialData.userStory || '',
      definitionOfDone: initialData.definitionOfDone || '',
      researchNotes: initialData.researchNotes || '',
      technicalApproach: initialData.technicalApproach || ''
    } : {
      title: '',
      description: '',
      type: '',
      severity: '',
      priority: '',
      status: 'NEW', // Default status for new issues
      assigneeId: '',
      devCompletionDate: '',
      qcCompletionDate: '',
      rootCause: '',
      targetDateChangeReason: '',
      versionRelease: '',
      environment: '',
      parentId: '',
      moduleId: '',
      submoduleId: '',
      reopenComment: '',
      // Conditional fields - default to empty for new issues
      stepsToReproduce: '',
      expectedResult: '',
      actualResult: '',
      businessJustification: '',
      acceptanceCriteria: '',
      userStory: '',
      definitionOfDone: '',
      researchNotes: '',
      technicalApproach: ''
    },
    validationSchema,
    onSubmit: async (values) => {
      console.log('Form submission started with values:', values);
      console.log('Form mode:', isEditMode ? 'Edit' : 'Create');

      setIsSubmitting(true);
      try {
        // Prepare data for submission
        const issueData: any = {
          title: values.title,
          description: values.description,
          type: values.type,
          severity: values.severity,
          priority: values.priority,
          status: values.status,
          versionRelease: values.versionRelease || null,
          environment: values.environment,
          rootCause: values.rootCause || null,
          // Conditional fields
          stepsToReproduce: values.stepsToReproduce || null,
          expectedResult: values.expectedResult || null,
          actualResult: values.actualResult || null,
          businessJustification: values.businessJustification || null,
          acceptanceCriteria: values.acceptanceCriteria || null,
          userStory: values.userStory || null,
          definitionOfDone: values.definitionOfDone || null,
          researchNotes: values.researchNotes || null,
          technicalApproach: values.technicalApproach || null
        };

        // Handle module
        if (values.moduleId) {
          issueData.module = { id: parseInt(values.moduleId) };
        } else {
          issueData.module = null;
        }

        // Handle submodule
        if (values.submoduleId) {
          issueData.submodule = { id: parseInt(values.submoduleId) };
        } else {
          issueData.submodule = null;
        }

        console.log('Prepared issue data:', issueData);

        // Handle assignee
        if (values.assigneeId) {
          issueData.assignee = { id: parseInt(values.assigneeId) };
        } else {
          issueData.assignee = null;
        }

        // Handle parent issue (only if not disabled and has valid value)
        if (values.parentId && values.parentId.trim() !== '' && !isNaN(parseInt(values.parentId))) {
          issueData.parent = { id: parseInt(values.parentId) };
        } else {
          issueData.parent = null;
        }

        // Handle dev completion date
        if (values.devCompletionDate) {
          try {
            const [year, month, day] = values.devCompletionDate.split('-').map(Number);
            // Validate the date components
            if (!isNaN(year) && !isNaN(month) && !isNaN(day)) {
              // Create an ISO string for the date (YYYY-MM-DDT00:00:00)
              const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}T00:00:00`;
              issueData.devCompletionDate = dateStr;
              console.log('Dev completion date set to:', issueData.devCompletionDate);
            } else {
              console.warn('Invalid dev completion date format:', values.devCompletionDate);
              issueData.devCompletionDate = null;
            }
          } catch (error) {
            console.error('Error parsing dev completion date:', error);
            issueData.devCompletionDate = null;
          }
        } else {
          issueData.devCompletionDate = null;
        }

        // Handle QC completion date
        if (values.qcCompletionDate) {
          try {
            const [year, month, day] = values.qcCompletionDate.split('-').map(Number);
            // Validate the date components
            if (!isNaN(year) && !isNaN(month) && !isNaN(day)) {
              // Create an ISO string for the date (YYYY-MM-DDT00:00:00)
              const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}T00:00:00`;
              issueData.qcCompletionDate = dateStr;
              console.log('QC completion date set to:', issueData.qcCompletionDate);
            } else {
              console.warn('Invalid QC completion date format:', values.qcCompletionDate);
              issueData.qcCompletionDate = null;
            }
          } catch (error) {
            console.error('Error parsing QC completion date:', error);
            issueData.qcCompletionDate = null;
          }
        } else {
          issueData.qcCompletionDate = null;
        }

        // For edit mode, dispatch update action
        if (isEditMode && issueId) {
          console.log('Updating issue with ID:', issueId);
          const updateData = {
            id: issueId,
            ...issueData,
            reopenComment: values.reopenComment
          };
          console.log('Update data being sent to API:', updateData);

          try {
            const result = await dispatch(updateIssue(updateData)).unwrap();
            console.log('Update issue API call successful:', result);
          } catch (updateError: any) {
            console.error('Update issue API call failed:', updateError);
            throw updateError;
          }
        } else {
          // For create mode, dispatch create action
          console.log('Creating new issue');
          console.log('Create data being sent to API:', issueData);

          try {
            const result = await dispatch(createIssue(issueData)).unwrap();
            console.log('Create issue API call successful:', result);
          } catch (createError: any) {
            console.error('Create issue API call failed:', createError);
            throw createError;
          }
        }

        // Clear any previous errors
        setFormError(null);

        // Call the success callback
        onSubmitSuccess();
      } catch (error: any) {
        console.error('Error in form submission:', error);
        // Extract error message from the response if available
        if (error.response && error.response.data) {
          const errorData = error.response.data;
          setFormError(errorData.message || 'Failed to submit form. Please check your inputs.');
          console.error('Server error details:', errorData);
        } else {
          setFormError('Failed to submit form. Please try again later.');
        }
      } finally {
        setIsSubmitting(false);
      }
    }
  });

  // Fetch conditional fields when issue type changes
  useEffect(() => {
    const fetchConditionalFields = async () => {
      const issueType = formik?.values?.type;
      if (issueType) {
        try {
          const [requiredFields, optionalFields, displayInfo] = await Promise.all([
            issueTypeFieldConfigService.getRequiredFields(issueType),
            issueTypeFieldConfigService.getOptionalFields(issueType),
            issueTypeFieldConfigService.getFieldDisplayInfo(issueType)
          ]);

          setConditionalFields({
            required: requiredFields,
            optional: optionalFields,
            displayInfo
          });
          setCurrentIssueType(issueType);
        } catch (error) {
          console.error('Error fetching conditional fields:', error);
          // Use fallback empty state
          setConditionalFields({
            required: [],
            optional: [],
            displayInfo: {}
          });
        }
      } else {
        // Clear conditional fields when no type is selected
        setConditionalFields({
          required: [],
          optional: [],
          displayInfo: {}
        });
        setCurrentIssueType('');
      }
    };

    fetchConditionalFields();
  }, [formik?.values?.type]);

  // Handle voice assistant data from navigation state (after formik initialization)
  useEffect(() => {
    if (location.state?.fromVoiceAssistant && location.state?.voiceData && formik && formik.setValues) {
      const voiceData = location.state.voiceData;
      const aiEnhancement = location.state.aiEnhancement;

      console.log('Received voice data:', voiceData);
      console.log('Received AI enhancement:', aiEnhancement);

      // Apply voice data to form with safety check
      try {
        formik.setValues({
          ...formik.values,
          title: aiEnhancement?.title || voiceData.issueDescription.substring(0, 100),
          description: aiEnhancement?.description || voiceData.issueDescription,
          type: aiEnhancement?.type || 'BUG',
          severity: aiEnhancement?.severity || voiceData.severity || 'NORMAL',
          priority: aiEnhancement?.priority || voiceData.priority || 'NORMAL',
          environment: aiEnhancement?.environment || voiceData.environment || 'PRODUCTION'
        });

        setVoiceGenerated(true);
        setAiGenerated(!!aiEnhancement);
        setAiAssistEnabled(true);

        // Clear navigation state to prevent re-application
        navigate(location.pathname, { replace: true, state: {} });
      } catch (error) {
        console.error('Error applying voice data to form:', error);
      }
    }
  }, [location.state, navigate, formik]);

  // Auto-select single submodule when submodule options change (after formik initialization)
  useEffect(() => {
    if (selectedModuleId && submoduleOptions && submoduleOptions.length === 1 && formik && formik.setFieldValue && formik.values && !formik.values.submoduleId) {
      console.log('Auto-selecting single submodule:', submoduleOptions[0]);
      try {
        formik.setFieldValue('submoduleId', submoduleOptions[0].value.toString());
      } catch (error) {
        console.error('Error auto-selecting submodule:', error);
      }
    }
  }, [selectedModuleId, submoduleOptions, formik]);

  // Debug form validation state
  useEffect(() => {
    if (formik) {
      console.log('Form validation state:', {
        isValid: formik.isValid,
        dirty: formik.dirty,
        errors: formik.errors,
        touched: formik.touched,
        values: formik.values
      });
    }
  }, [formik?.isValid, formik?.errors, formik?.touched, formik?.values]);

  // Handle AI assistance application
  const handleAIAssistanceApply = (aiData: any) => {
    console.log('Applying AI assistance data:', aiData);

    // Update form values with AI suggestions - with safety check
    if (formik && formik.setValues && formik.values) {
      try {
        formik.setValues({
          ...formik.values,
          title: aiData.title || formik.values.title,
          description: aiData.description || formik.values.description,
          type: aiData.type || formik.values.type,
          severity: aiData.severity || formik.values.severity,
          priority: aiData.priority || formik.values.priority,
          environment: aiData.environment || formik.values.environment,
          // Keep existing values for fields not provided by AI
        });

        setAiGenerated(true);
        setAiDialogOpen(false);
      } catch (error) {
        console.error('Error applying AI assistance data:', error);
        setAiDialogOpen(false);
      }
    } else {
      console.warn('Formik not ready for AI assistance application');
      setAiDialogOpen(false);
    }
  };

  // Loading state
  if (lookupLoading || modulesLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Safety check: ensure formik is properly initialized
  if (!formik || !formik.values || !formik.handleSubmit) {
    console.warn('Formik not properly initialized, showing loading state');
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Render form with dynamic dropdowns
  return (
    <Box component="form" onSubmit={formik.handleSubmit} sx={{ width: '100%', p: 3, overflowY: 'auto' }}>
      {/* AI Assistance Section */}
      {!isEditMode && (
        <Paper
          elevation={0}
          sx={{
            p: 2,
            mb: 3,
            border: '1px solid',
            borderColor: aiAssistEnabled ? 'primary.main' : 'grey.300',
            borderRadius: 2,
            bgcolor: aiAssistEnabled ? 'primary.50' : 'grey.50'
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <AIIcon color={aiAssistEnabled ? 'primary' : 'disabled'} />
              <Typography variant="h6" color={aiAssistEnabled ? 'primary' : 'text.secondary'}>
                AI-Assisted Issue Creation
              </Typography>
              {voiceGenerated && (
                <Chip
                  label="Voice Generated"
                  size="small"
                  color="secondary"
                  variant="outlined"
                  sx={{ mr: 1 }}
                />
              )}
              {aiGenerated && (
                <Chip
                  label="AI Enhanced"
                  size="small"
                  color="primary"
                  variant="outlined"
                />
              )}
            </Box>
            <FormControlLabel
              control={
                <Switch
                  checked={aiAssistEnabled}
                  onChange={(e) => setAiAssistEnabled(e.target.checked)}
                  color="primary"
                />
              }
              label=""
            />
          </Box>

          {aiAssistEnabled && (
            <Box>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Choose your preferred method to create a comprehensive issue report.
              </Typography>

              {/* Voice Assistant Alert */}
              {voiceGenerated && (
                <Alert severity="success" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    <strong>Voice input captured!</strong> Your spoken description has been processed
                    {aiGenerated ? ' and enhanced with AI assistance.' : '.'}
                  </Typography>
                </Alert>
              )}

              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <Button
                  variant="outlined"
                  startIcon={<AIIcon />}
                  onClick={() => setAiDialogOpen(true)}
                  size="small"
                >
                  Text + AI Assistance
                </Button>

                <Button
                  variant="contained"
                  startIcon={<VoiceIcon />}
                  onClick={() => {
                    navigate('/voice-assistant', {
                      state: { returnPath: location.pathname }
                    });
                  }}
                  size="small"
                  color="secondary"
                  sx={{ fontWeight: 'bold' }}
                >
                  Enhanced Voice Assistant
                </Button>

                <Button
                  variant="outlined"
                  startIcon={<MicIcon />}
                  onClick={() => {
                    setAiDialogOpen(true);
                    // The dialog will handle switching to voice mode
                  }}
                  size="small"
                  color="secondary"
                >
                  Quick Voice Input
                </Button>

                {(aiGenerated || voiceGenerated) && (
                  <Button
                    variant="text"
                    size="small"
                    onClick={() => {
                      setAiGenerated(false);
                      setVoiceGenerated(false);
                      formik.resetForm();
                    }}
                  >
                    Clear All Suggestions
                  </Button>
                )}
              </Box>

              {/* Feature descriptions */}
              <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                <Typography variant="caption" color="text.secondary" display="block" sx={{ mb: 1 }}>
                  <strong>Enhanced Voice Assistant:</strong> Full conversational experience with Waldo AI
                </Typography>
                <Typography variant="caption" color="text.secondary" display="block" sx={{ mb: 1 }}>
                  <strong>Quick Voice Input:</strong> Simple voice recording with text conversion
                </Typography>
                <Typography variant="caption" color="text.secondary" display="block">
                  <strong>Text + AI:</strong> Traditional text input with AI enhancement
                </Typography>
              </Box>
            </Box>
          )}
        </Paper>
      )}

      <Grid container spacing={2}>
        {/* Title field */}
        <Grid item xs={12}>
          <TextField
            fullWidth
            id="title"
            name="title"
            label="Title"
            value={formik.values.title}
            onChange={formik.handleChange}
            error={formik.touched.title && Boolean(formik.errors.title)}
            helperText={formik.touched.title && formik.errors.title}
            InputLabelProps={{
              sx: { background: 'white', px: 0.5 }
            }}
            sx={commonFieldSx}
          />
        </Grid>

        {/* Description field - reduced size */}
        <Grid item xs={12}>
          <TextField
            fullWidth
            id="description"
            name="description"
            label="Description"
            multiline
            minRows={2}
            maxRows={4}
            value={formik.values.description}
            onChange={formik.handleChange}
            error={formik.touched.description && Boolean(formik.errors.description)}
            helperText={formik.touched.description && formik.errors.description}
            InputLabelProps={{
              sx: { background: 'white', px: 0.5 }
            }}
            sx={{
              ...textAreaSx,
              '& .MuiOutlinedInput-root': {
                ...textAreaSx['& .MuiOutlinedInput-root'],
                resize: 'vertical',
                '& textarea': {
                  resize: 'vertical',
                  minHeight: '48px', // Equivalent to 2 rows
                  maxHeight: '96px', // Equivalent to 4 rows
                }
              }
            }}
          />
        </Grid>

        {/* Type dropdown - using lookup values */}
        <Grid item xs={12} md={6}>
          <FormControl
            fullWidth
            error={formik.touched.type && Boolean(formik.errors.type)}
          >
            <InputLabel>Type</InputLabel>
            <Select
              id="type"
              name="type"
              label="Type"
              value={formik.values.type}
              onChange={formik.handleChange}
              MenuProps={{ PaperProps: { sx: { maxHeight: 300 } } }}
              sx={{ '& .MuiSelect-select': { padding: '10px 14px' } }}
            >
              {issueType?.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
            {formik.touched.type && formik.errors.type && (
              <FormHelperText>{formik.errors.type as string}</FormHelperText>
            )}
          </FormControl>
        </Grid>

        {/* Severity dropdown - using lookup values */}
        <Grid item xs={12} md={6}>
          <FormControl
            fullWidth
            error={formik.touched.severity && Boolean(formik.errors.severity)}
          >
            <InputLabel>Severity</InputLabel>
            <Select
              id="severity"
              name="severity"
              label="Severity"
              value={formik.values.severity}
              onChange={async (e) => {
                const newSeverity = e.target.value;
                const oldSeverity = formik.values.severity;

                // Handle severity change logic
                if (newSeverity === 'CRITICAL') {
                  // Auto-set priority to Critical (business rule)
                  formik.setFieldValue('priority', 'CRITICAL');

                  // REMOVED: Dev completion date auto-population
                  // Business Rule: All completion dates must be manually entered by users
                  // No auto-population based on SLA or severity level
                } else if (oldSeverity === 'CRITICAL' && newSeverity !== 'CRITICAL') {
                  // Show confirmation dialog when changing FROM Critical
                  const currentDevDate = formik.values.devCompletionDate;
                  if (currentDevDate) {
                    const keepDate = window.confirm(
                      'You are changing the severity from Critical to ' + newSeverity + '. ' +
                      'Do you want to retain the current Dev Completion Date (' + currentDevDate + ')?\n\n' +
                      'Click Yes to retain the Dev Completion Date, or No to clear it.'
                    );

                    if (!keepDate) {
                      // Properly clear the dev completion date field
                      // Use both setFieldValue and setFieldTouched to ensure complete clearing
                      formik.setFieldValue('devCompletionDate', '');
                      formik.setFieldTouched('devCompletionDate', false);

                      // Force a re-render to ensure the field appears empty
                      setTimeout(() => {
                        const devDateField = document.getElementById('devCompletionDate') as HTMLInputElement;
                        if (devDateField) {
                          devDateField.value = '';
                        }
                      }, 0);
                    }
                  }
                }

                formik.setFieldValue('severity', newSeverity);
              }}
              MenuProps={{ PaperProps: { sx: { maxHeight: 300 } } }}
              sx={{ '& .MuiSelect-select': { padding: '10px 14px' } }}
            >
              {issueSeverity?.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
            {formik.touched.severity && formik.errors.severity && (
              <FormHelperText>{formik.errors.severity as string}</FormHelperText>
            )}
          </FormControl>
        </Grid>

        {/* Priority dropdown - using lookup values */}
        <Grid item xs={12} md={6}>
          <FormControl
            fullWidth
            error={formik.touched.priority && Boolean(formik.errors.priority)}
          >
            <InputLabel>Priority</InputLabel>
            <Select
              id="priority"
              name="priority"
              label="Priority"
              value={formik.values.priority}
              onChange={formik.handleChange}
              MenuProps={{ PaperProps: { sx: { maxHeight: 300 } } }}
              sx={{ '& .MuiSelect-select': { padding: '10px 14px' } }}
            >
              {issuePriority?.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
            {formik.touched.priority && formik.errors.priority && (
              <FormHelperText>{formik.errors.priority as string}</FormHelperText>
            )}
          </FormControl>
        </Grid>

        {/* Status dropdown - using lookup values */}
        <Grid item xs={12} md={6}>
          <FormControl
            fullWidth
            error={formik.touched.status && Boolean(formik.errors.status)}
          >
            <InputLabel>Status</InputLabel>
            <Select
              id="status"
              name="status"
              label="Status"
              value={formik.values.status}
              onChange={(e) => {
                const newStatus = e.target.value;

                // Validate that REOPENED can only be selected from specific statuses
                if (newStatus === 'REOPENED' && initialData) {
                  const allowedStatuses = ['DEV_COMPLETED', 'QC_COMPLETED', 'UAT_COMPLETED', 'UNDER_REVIEW', 'CLOSED'];
                  if (!allowedStatuses.includes(initialData.status)) {
                    formik.setFieldError('status', 'Issues can only be reopened from Dev Completed, QC Completed, UAT Completed, Under Review, or Closed status');
                    return;
                  }
                }

                formik.setFieldValue('status', newStatus);
              }}
              MenuProps={{ PaperProps: { sx: { maxHeight: 300 } } }}
              sx={{ '& .MuiSelect-select': { padding: '10px 14px' } }}
            >
              {issueStatus?.filter((option) => {
                // Filter out REOPENED option if current status doesn't allow it
                if (option.value === 'REOPENED') {
                  // For new issues (no initialData), don't show REOPENED
                  if (!initialData) return false;

                  // Only show REOPENED if current status allows it
                  const allowedStatuses = ['DEV_COMPLETED', 'QC_COMPLETED', 'UAT_COMPLETED', 'UNDER_REVIEW', 'CLOSED'];
                  return allowedStatuses.includes(initialData.status);
                }
                return true;
              }).map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
            {formik.touched.status && formik.errors.status && (
              <FormHelperText>{formik.errors.status as string}</FormHelperText>
            )}
          </FormControl>
        </Grid>

        {/* Environment dropdown - using lookup values */}
        <Grid item xs={12} md={6}>
          <FormControl
            fullWidth
            error={formik.touched.environment && Boolean(formik.errors.environment)}
          >
            <InputLabel>Environment</InputLabel>
            <Select
              id="environment"
              name="environment"
              label="Environment"
              value={formik.values.environment}
              onChange={formik.handleChange}
              MenuProps={{ PaperProps: { sx: { maxHeight: 300 } } }}
              sx={{ '& .MuiSelect-select': { padding: '10px 14px' } }}
            >
              {issueEnvironment?.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
            {formik.touched.environment && formik.errors.environment && (
              <FormHelperText>{formik.errors.environment as string}</FormHelperText>
            )}
          </FormControl>
        </Grid>

        {/* Module dropdown */}
        <Grid item xs={12} md={6}>
          <FormControl
            fullWidth
            error={formik.touched.moduleId && Boolean(formik.errors.moduleId)}
          >
            <InputLabel>Module</InputLabel>
            <Select
              id="moduleId"
              name="moduleId"
              label="Module"
              value={formik.values.moduleId}
              onChange={(e) => {
                // Prevent any default behavior that might cause page refresh
                e.preventDefault();
                const newModuleId = e.target.value;

                // Immediately update form values without setTimeout
                formik.setFieldValue('moduleId', newModuleId);
                // Clear submodule when module changes
                formik.setFieldValue('submoduleId', '');
                // Update selected module for submodule fetching
                setSelectedModuleId(newModuleId ? parseInt(newModuleId) : null);
              }}
              MenuProps={{ PaperProps: { sx: { maxHeight: 300 } } }}
              sx={{ '& .MuiSelect-select': { padding: '10px 14px' } }}
            >
              <MenuItem value="">
                <em>Select Module</em>
              </MenuItem>
              {moduleOptions?.map((option) => (
                <MenuItem key={option.value} value={option.value.toString()}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
            {formik.touched.moduleId && formik.errors.moduleId && (
              <FormHelperText>{formik.errors.moduleId as string}</FormHelperText>
            )}
          </FormControl>
        </Grid>

        {/* Submodule dropdown */}
        <Grid item xs={12} md={6}>
          <FormControl
            fullWidth
            error={formik.touched.submoduleId && Boolean(formik.errors.submoduleId)}
            disabled={!formik.values.moduleId}
          >
            <InputLabel>Submodule</InputLabel>
            <Select
              id="submoduleId"
              name="submoduleId"
              label="Submodule"
              value={formik.values.submoduleId}
              onChange={formik.handleChange}
              MenuProps={{ PaperProps: { sx: { maxHeight: 300 } } }}
              sx={{ '& .MuiSelect-select': { padding: '10px 14px' } }}
            >
              <MenuItem value="">
                <em>Select Submodule</em>
              </MenuItem>
              {submoduleOptions?.map((option) => (
                <MenuItem key={option.value} value={option.value.toString()}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
            {formik.touched.submoduleId && formik.errors.submoduleId && (
              <FormHelperText>{formik.errors.submoduleId as string}</FormHelperText>
            )}
            {!formik.values.moduleId && (
              <FormHelperText>Please select a module first</FormHelperText>
            )}
          </FormControl>
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControl
            fullWidth
            error={formik.touched.assigneeId && Boolean(formik.errors.assigneeId)}
          >
            <InputLabel>Assignee</InputLabel>
            <Select
              id="assigneeId"
              name="assigneeId"
              label="Assignee"
              value={formik.values.assigneeId}
              onChange={formik.handleChange}
              MenuProps={{ PaperProps: { sx: { maxHeight: 300 } } }}
              sx={{ '& .MuiSelect-select': { padding: '10px 14px' } }}
            >
              <MenuItem value="">
                <em>Select Assignee</em>
              </MenuItem>
              {Array.isArray(users) && users.map((user) => (
                <MenuItem key={user.id} value={user.id.toString()}>
                  {user.username}
                </MenuItem>
              ))}
            </Select>
            {formik.touched.assigneeId && formik.errors.assigneeId && (
              <FormHelperText>{formik.errors.assigneeId as string}</FormHelperText>
            )}
          </FormControl>
        </Grid>

        {/* Conditional Fields Section */}
        {(conditionalFields.required.length > 0 || conditionalFields.optional.length > 0) && (
          <>
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }}>
                <Typography variant="h6" color="text.secondary" sx={{ px: 2 }}>
                  {formik.values.type} Specific Fields
                </Typography>
              </Divider>
            </Grid>

            {/* Render conditional fields in the order provided by backend */}
            {Object.keys(conditionalFields.displayInfo).map((fieldName) => {
              const displayInfo = conditionalFields.displayInfo[fieldName];
              const isRequired = conditionalFields.required.includes(fieldName);

              if (!displayInfo) return null;

              return (
                <Grid item xs={12} key={fieldName}>
                  <TextField
                    fullWidth
                    id={fieldName}
                    name={fieldName}
                    label={`${displayInfo.label}${isRequired ? ' *' : ''}`}
                    multiline
                    minRows={3}
                    maxRows={8}
                    value={formik.values[fieldName as keyof typeof formik.values] || ''}
                    onChange={formik.handleChange}
                    error={formik.touched[fieldName as keyof typeof formik.touched] && Boolean(formik.errors[fieldName as keyof typeof formik.errors])}
                    helperText={
                      (formik.touched[fieldName as keyof typeof formik.touched] && formik.errors[fieldName as keyof typeof formik.errors]) ||
                      displayInfo.description
                    }
                    placeholder={displayInfo.placeholder}
                    required={isRequired}
                    InputLabelProps={{
                      sx: { background: 'white', px: 0.5 }
                    }}
                    sx={{
                      ...textAreaSx,
                      '& .MuiOutlinedInput-root': {
                        ...textAreaSx['& .MuiOutlinedInput-root'],
                        resize: 'vertical',
                        '& textarea': {
                          resize: 'vertical',
                          minHeight: '80px', // Same as description field
                          maxHeight: '192px', // Equivalent to 8 rows
                          fontWeight: 'normal',
                          color: 'text.primary'
                        }
                      }
                    }}
                  />
                </Grid>
              );
            })}
          </>
        )}

        {/* Dev Completion Date field - MANUAL ENTRY ONLY, NO AUTO-POPULATION */}
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            id="devCompletionDate"
            name="devCompletionDate"
            label="Dev Completion Date (Manual Entry Only)"
            type="date"
            InputLabelProps={{
              shrink: true,
              sx: { background: 'white', px: 0.5 }
            }}
            inputProps={{
              min: new Date().toISOString().split('T')[0]
            }}
            value={formik.values.devCompletionDate || ''}
            onChange={(e) => {
              const newValue = e.target.value;
              const oldValue = formik.values.devCompletionDate;
              formik.setFieldValue('devCompletionDate', newValue);

              // If this is an edit and the dev completion date has changed, show the reason field
              if (isEditMode && initialData && oldValue && newValue !== oldValue) {
                formik.setFieldTouched('targetDateChangeReason', true);
              }
            }}
            error={formik.touched.devCompletionDate && Boolean(formik.errors.devCompletionDate)}
            helperText={formik.touched.devCompletionDate && formik.errors.devCompletionDate}
            sx={{
              ...commonFieldSx,
              '& input[type="date"]::-webkit-calendar-picker-indicator': {
                cursor: 'pointer',
                filter: 'invert(0.5)',
                fontSize: '1.2em',
                opacity: 0.8,
                '&:hover': {
                  opacity: 1,
                  filter: 'invert(0.7)',
                },
              },
            }}
          />
        </Grid>

        {/* QC Completion Date field - MUST REMAIN MANUAL ONLY, NO AUTO-POPULATION */}
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            id="qcCompletionDate"
            name="qcCompletionDate"
            label="QC Completion Date (Manual Entry Only)"
            type="date"
            InputLabelProps={{
              shrink: true,
              sx: { background: 'white', px: 0.5 }
            }}
            inputProps={{
              min: new Date().toISOString().split('T')[0]
            }}
            value={formik.values.qcCompletionDate || ''}
            onChange={formik.handleChange} // Manual selection only - no auto-population allowed
            error={formik.touched.qcCompletionDate && Boolean(formik.errors.qcCompletionDate)}
            helperText={formik.touched.qcCompletionDate && formik.errors.qcCompletionDate}
            sx={{
              ...commonFieldSx,
              '& input[type="date"]::-webkit-calendar-picker-indicator': {
                cursor: 'pointer',
                filter: 'invert(0.5)',
                fontSize: '1.2em',
                opacity: 0.8,
                '&:hover': {
                  opacity: 1,
                  filter: 'invert(0.7)',
                },
              },
            }}
          />
        </Grid>

        {/* Root Cause field - show when status is Dev Completed or when it has a value */}
        {(formik.values.status === 'DEV_COMPLETED' || formik.values.rootCause) && (
          <Grid item xs={12}>
            <TextField
              fullWidth
              id="rootCause"
              name="rootCause"
              label="Root Cause"
              multiline
              rows={3}
              value={formik.values.rootCause}
              onChange={formik.handleChange}
              error={formik.touched.rootCause && Boolean(formik.errors.rootCause)}
              helperText={formik.touched.rootCause && formik.errors.rootCause || 'Required when status is Dev Completed'}
              InputLabelProps={{
                sx: { background: 'white', px: 0.5 }
              }}
              sx={textAreaSx}
              required={formik.values.status === 'DEV_COMPLETED'}
              disabled={false} // Remove isViewMode reference as it's not defined in this component
            />
          </Grid>
        )}

        {/* Dev Completion Date Change Reason - only show if dev completion date has changed in edit mode */}
        {isEditMode && initialData && formik.values.devCompletionDate &&
         (initialData.devCompletionDate || initialData.targetDate) &&
         formik.values.devCompletionDate !== formatDateFromBackend(initialData.devCompletionDate || initialData.targetDate) && (
          <Grid item xs={12}>
            <Paper
              elevation={0}
              sx={{
                p: 2,
                mb: 2,
                borderRadius: 2,
                bgcolor: 'info.light',
                color: 'info.contrastText',
                border: '1px solid',
                borderColor: 'info.main'
              }}
            >
              <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'bold' }}>
                Dev Completion Date Change Detected
              </Typography>
              <Typography variant="body2" sx={{ mb: 2 }}>
                You are changing the dev completion date from {(initialData.devCompletionDate || initialData.targetDate) ? formatDateDisplay(initialData.devCompletionDate || initialData.targetDate) : 'Not set'} to {formik.values.devCompletionDate ? formatDateDisplay(formik.values.devCompletionDate) : 'Not set'}.
                Please provide a reason for this change.
              </Typography>
            </Paper>
            <TextField
              fullWidth
              id="targetDateChangeReason"
              name="targetDateChangeReason"
              label="Reason for Dev Completion Date Change"
              multiline
              rows={2}
              value={formik.values.targetDateChangeReason}
              onChange={formik.handleChange}
              error={formik.touched.targetDateChangeReason && Boolean(formik.errors.targetDateChangeReason)}
              helperText={formik.touched.targetDateChangeReason && formik.errors.targetDateChangeReason || 'This reason will be recorded in the issue history'}
              InputLabelProps={{
                sx: { background: 'white', px: 0.5 }
              }}
              sx={textAreaSx}
              required
            />
          </Grid>
        )}

        {/* Reopen Comment - only show when status is being changed to REOPENED */}
        {formik.values.status === 'REOPENED' && (
          <Grid item xs={12}>
            <Paper
              elevation={0}
              sx={{
                p: 2,
                mb: 2,
                borderRadius: 2,
                bgcolor: 'warning.light',
                color: 'warning.contrastText',
                border: '1px solid',
                borderColor: 'warning.main'
              }}
            >
              <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'bold' }}>
                Reopen Issue
              </Typography>
              <Typography variant="body2" sx={{ mb: 2 }}>
                You are reopening this issue. Please provide a detailed reason for reopening.
              </Typography>
              <TextField
                fullWidth
                id="reopenComment"
                name="reopenComment"
                label="Reason for Reopening"
                multiline
                rows={3}
                value={formik.values.reopenComment}
                onChange={formik.handleChange}
                error={formik.touched.reopenComment && Boolean(formik.errors.reopenComment)}
                helperText={formik.touched.reopenComment && formik.errors.reopenComment || 'This comment will be added to the issue history'}
                InputLabelProps={{
                  sx: { background: 'white', px: 0.5 }
                }}
                sx={textAreaSx}
                required
              />
            </Paper>
          </Grid>
        )}

        {/* Completion Date - read-only, shown if issue is closed or has a completion date */}
        {initialData && (initialData.completionDate || initialData.status === 'CLOSED') && (
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              id="completionDate"
              label="Completion Date"
              value={initialData.completionDate ? formatDateDisplay(initialData.completionDate) : 'Set automatically on close'}
              InputProps={{
                readOnly: true,
              }}
              InputLabelProps={{
                shrink: true,
                sx: { background: 'white', px: 0.5 }
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 8,
                  backgroundColor: 'white',
                  border: '1px solid #d1d5db',
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: 'transparent'
                  }
                }
              }}
            />
          </Grid>
        )}

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            id="versionRelease"
            name="versionRelease"
            label="Version/Release"
            value={formik.values.versionRelease}
            onChange={formik.handleChange}
            error={formik.touched.versionRelease && Boolean(formik.errors.versionRelease)}
            helperText={formik.touched.versionRelease && formik.errors.versionRelease ? formik.errors.versionRelease as string : ''}
            InputLabelProps={{
              sx: { background: 'white', px: 0.5 }
            }}
            sx={commonFieldSx}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControl
            fullWidth
            error={formik.touched.parentId && Boolean(formik.errors.parentId)}
          >
            <InputLabel>Parent Issue</InputLabel>
            <Select
              id="parentId"
              name="parentId"
              label="Parent Issue"
              value={formik.values.parentId}
              onChange={formik.handleChange}
              MenuProps={{ PaperProps: { sx: { maxHeight: 300 } } }}
              sx={{ '& .MuiSelect-select': { padding: '10px 14px' } }}
              disabled // Disabled until parent issues functionality is implemented
            >
              <MenuItem value="">
                <em>No parent issue</em>
              </MenuItem>
              {/* Parent issues would be populated here when functionality is implemented */}
            </Select>
            {formik.touched.parentId && formik.errors.parentId && (
              <FormHelperText>{formik.errors.parentId as string}</FormHelperText>
            )}
          </FormControl>
        </Grid>

        {/* Error message */}
        {formError && (
          <Grid item xs={12}>
            <Paper
              elevation={0}
              sx={{
                p: 2,
                mb: 2,
                borderRadius: 2,
                bgcolor: 'error.light',
                color: 'error.contrastText',
                border: '1px solid',
                borderColor: 'error.main'
              }}
            >
              <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                Error
              </Typography>
              <Typography variant="body2">
                {formError}
              </Typography>
            </Paper>
          </Grid>
        )}

        {/* Submit buttons */}
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 3, pt: 2, borderTop: '1px solid', borderColor: 'divider' }}>
            <Button
              onClick={onCancel || (() => onSubmitSuccess(true))}
              sx={{ borderRadius: 2, fontWeight: 'medium', px: 3 }}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="contained"
              color="primary"
              disabled={isSubmitting || (!formik.isValid && Boolean(formik.touched.title))}
              sx={{ borderRadius: 2, fontWeight: 'medium', px: 3 }}
            >
              {isSubmitting ? 'Saving...' : isEditMode ? 'Update Issue' : 'Create Issue'}
            </Button>
          </Box>
        </Grid>
      </Grid>

      {/* AI Assistance Dialog */}
      <AIAssistDialog
        open={aiDialogOpen}
        onClose={() => setAiDialogOpen(false)}
        onApply={handleAIAssistanceApply}
      />
    </Box>
  );
};

export default IssueForm;
