package com.bugtracker.service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.bugtracker.model.Issue;
import com.bugtracker.model.User;

public interface IssueService {
    Issue createIssue(Issue issue);

    Optional<Issue> getIssueById(Long id);

    Optional<Issue> getIssueByIdWithDetails(Long id);

    Optional<Issue> getIssueByIdentifier(String identifier);

    Optional<Issue> getIssueByIdentifierWithDetails(String identifier);

    Page<Issue> getAllIssues(Pageable pageable);

    Page<Issue> getIssuesByAssignee(User assignee, Pageable pageable);

    Page<Issue> getIssuesByReporter(User reporter, Pageable pageable);



    List<Issue> getOverdueIssues();

    List<Issue> getActiveIssuesByAssignee(User assignee);

    List<Issue> getChildIssues(Issue parent);

    Page<Issue> getRootIssues(Pageable pageable);

    Page<Issue> searchIssues(String keyword, Pageable pageable);

    Issue updateIssue(Issue issue);

    void deleteIssue(Long id);

    String generateIssueIdentifier(Issue issue);

    void addWatcher(Long issueId, Long userId);

    void removeWatcher(Long issueId, Long userId);

    List<Issue> getRecentIssues(int limit);

    Object getDashboardStats(String period);

    Object getDashboardStats(String period, String environment);

    void addLabel(Long issueId, Long labelId);

    void removeLabel(Long issueId, Long labelId);

    Page<Issue> getFilteredIssues(String search, String status, String type, String priority, String environment, java.time.LocalDate startDate, java.time.LocalDate endDate, Pageable pageable);

    Page<Issue> convertListToPage(List<Issue> issues, Pageable pageable);

    Map<String, Object> bulkCreateIssues(List<Map<String, Object>> issuesData);
}
