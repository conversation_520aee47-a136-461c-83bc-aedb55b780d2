import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import fs from 'fs'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Load env file based on `mode` in the current directory.
  // Set the third parameter to '' to load all env regardless of the `VITE_` prefix.
  const env = loadEnv(mode, process.cwd(), '')

  // API configuration - Using HTTP for local development
  const API_PORT = env.VITE_API_PORT || '8080'
  const API_HOST = env.VITE_API_HOST || 'localhost'
  const DEV_PROXY_TARGET = `http://${API_HOST}:${API_PORT}`

  // SSL certificate paths
  const certPath = path.resolve(__dirname, '../certs/murthy.crt')
  const keyPath = path.resolve(__dirname, '../certs/murthy-ca.key')

  // Check if SSL certificates exist
  const sslEnabled = fs.existsSync(certPath) && fs.existsSync(keyPath)

  return {
    plugins: [react()],
    server: {
      port: 5173,
      host: true, // Allow external connections
      https: false, // Temporarily disable HTTPS for testing
      proxy: {
        '/api': {
          target: DEV_PROXY_TARGET,
          changeOrigin: true,
          secure: false, // Allow self-signed certificates in development
          configure: (proxy, options) => {
            // Handle HTTPS proxy configuration
            proxy.on('error', (err, req, res) => {
              console.log('Proxy error:', err);
            });
          }
        }
      }
    },
    preview: {
      port: 4173,
      host: true,
      https: sslEnabled ? {
        key: fs.readFileSync(keyPath),
        cert: fs.readFileSync(certPath),
      } : false,
    },
    // Build configuration for production
    build: {
      outDir: 'dist',
      sourcemap: true,
      // Ensure proper handling of client-side routing in production
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['react', 'react-dom', 'react-router-dom'],
            mui: ['@mui/material', '@mui/icons-material'],
          }
        }
      }
    }
  }
})
